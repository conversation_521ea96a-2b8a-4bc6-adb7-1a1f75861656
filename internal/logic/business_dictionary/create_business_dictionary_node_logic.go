package business_dictionary

import (
	"context"
	"errors"
	"strings"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateBusinessDictionaryNodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateBusinessDictionaryNodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBusinessDictionaryNodeLogic {
	return &CreateBusinessDictionaryNodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBusinessDictionaryNodeLogic) CreateBusinessDictionaryNode(req *types.CreateBusinessDictionaryNodeReq) (resp *types.CreateBusinessDictionaryNodeResp, err error) {
	dictionaryNodeClient := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB)
	count, err := dictionaryNodeClient.GetBusinessDictionaryNodeCountsByNameOrCode(l.ctx, req.DictionaryID, req.ParentID, req.Name, req.Code)
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.New("该字典已存在，请修改后保存")
	}

	nodes, err := dictionaryNodeClient.GetBusinessDictionaryNodesByDictionaryIDAndParentID(l.ctx, req.DictionaryID, req.ParentID)
	if err != nil {
		return nil, err
	}

	newNodes := l.sortNodes(nodes, req)

	tx := mapper.NewNebulaTXGenerator(l.svcCtx.NebulaDB).CreateTX(l.ctx)
	defer tx.AutoCommit(&err)

	err = dictionaryNodeClient.SaveBusinessDictionaryNodes(l.ctx, tx, newNodes)
	if err != nil {
		return nil, err
	}

	err = mapper.NewBusinessDictionaryClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryUpdatedAt(l.ctx, tx, req.DictionaryID, time.Now(), utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}

	// 新建节点成功后，维护 relation 表
	relations, err := l.buildNodeRelations(newNodes[len(newNodes)-1], tx)
	if err != nil {
		return nil, err
	}

	if len(relations) > 0 {
		err = mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).BatchUpsertRelations(l.ctx, tx, relations)
		if err != nil {
			return nil, err
		}
	}

	return &types.CreateBusinessDictionaryNodeResp{}, err
}

func (l *CreateBusinessDictionaryNodeLogic) buildNodeRelations(newNode mapper.BusinessDictionaryNode, tx *mapper.NebulaTX) ([]mapper.BusinessDictionaryNodeRelation, error) {
	ancestryMap := make(map[string][]mapper.BusinessDictionaryNode)
	ancestry, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetNodeAncestry(l.ctx, tx, newNode.ID)
	if err != nil {
		return nil, err
	}
	ancestryMap[newNode.ID] = ancestry
	return buildNodeRelations([]mapper.BusinessDictionaryNode{newNode}, ancestryMap), nil
}

func (l *CreateBusinessDictionaryNodeLogic) sortNodes(nodes []mapper.BusinessDictionaryNode, req *types.CreateBusinessDictionaryNodeReq) []mapper.BusinessDictionaryNode {
	newNodes := make([]mapper.BusinessDictionaryNode, len(nodes)+1)
	for i, node := range nodes {
		node.Sort++
		newNodes[i] = node
	}

	newNodes[len(newNodes)-1] = mapper.BusinessDictionaryNode{
		ID:           l.svcCtx.IdGenerator.GenerateIDString(),
		DictionaryID: req.DictionaryID,
		ParentID:     req.ParentID,
		Code:         req.Code,
		Name:         req.Name,
		Remark:       req.Remark,
		Status:       true,
		Sort:         1,
		CreatedAt:    time.Now(),
		CreatedBy:    utils.GetContextUserID(l.ctx),
		UpdatedAt:    time.Now(),
		UpdatedBy:    utils.GetContextUserID(l.ctx),
	}
	return newNodes
}

// 构造节点与其父链的 codes/names 拼接字符串
func buildNodeRelations(nodes []mapper.BusinessDictionaryNode, ancestryMap map[string][]mapper.BusinessDictionaryNode) []mapper.BusinessDictionaryNodeRelation {
	var relations []mapper.BusinessDictionaryNodeRelation
	for _, node := range nodes {
		ancestry := ancestryMap[node.ID]
		var codes, names string
		for _, n := range ancestry {
			if n.Code != "" {
				codes += n.Code + "-"
			}
			if n.Name != "" {
				names += n.Name + "-"
			}
		}
		codes = strings.TrimSuffix(codes, "-")
		names = strings.TrimSuffix(names, "-")
		relations = append(relations, mapper.BusinessDictionaryNodeRelation{
			NodeID:       node.ID,
			DictionaryID: node.DictionaryID,
			Codes:        codes,
			Names:        names,
		})
	}
	return relations
}
