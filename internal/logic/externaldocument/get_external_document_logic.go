package externaldocument

import (
	"context"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetExternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetExternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetExternalDocumentLogic {
	return &GetExternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetExternalDocumentLogic) GetExternalDocument(req *types.GetExternalDocumentReq) (resp *types.ExternalDocumentInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
