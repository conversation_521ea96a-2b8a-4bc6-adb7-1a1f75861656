package file_management_books

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"time"
)

type CreateBookLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateBookLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateBookLogic {
	return &CreateBookLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateBookLogic) CreateBook(req *types.CreateBookReq) (resp *types.CreateBookResp, err error) {
	currentLoginUser := utils.GetCurrentLoginUser(l.ctx)
	organizationInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, currentLoginUser.OrganizationId)
	if err != nil {
		return nil, err
	}

	bookType, bookNumber, err := GetBookTypeAndBookNumber(req.DictionaryNodeID, l.svcCtx.NebulaDB, l.ctx)
	if err != nil {
		return nil, err
	}

	data, err := docvault.NewBookClient(l.svcCtx.DocvaultRpcConn).CreateBook(l.ctx, &docvault.CreateBookReq{
		DictionaryNodeId: req.DictionaryNodeID,
		Name:             req.Name,
		Author:           req.Author,
		Publisher:        req.Publisher,
		RegisterCount:    req.RegisterCount,
		UserId:           currentLoginUser.UserId,
		OrganizationId:   currentLoginUser.OrganizationId,
		OrganizationCode: organizationInfo.Code,
		BookType:         bookType,
		BookNumber:       bookNumber,
		FileId:           req.FileID,
	})
	if err != nil {
		l.Logger.Errorf("新增书籍信息失败：%v", err)
		return nil, err
	}

	err = l.createBusinessDictionaryRelation(req.DictionaryNodeID, data.Id)
	if err != nil {
		return nil, err
	}

	return &types.CreateBookResp{}, nil
}

func GetBookTypeAndBookNumber(dictionaryNodeID string, db *mapper.NebulaDB, ctx context.Context) (string, string, error) {
	dictionaryNodeRelation, err := mapper.NewBusinessDictionaryNodeRelationClient(db).GetBusinessDictionaryNodeRelationByNodeID(ctx, dictionaryNodeID)
	if err != nil {
		return "", "", err
	}
	bookType := dictionaryNodeRelation.Names
	bookNumber := ""
	if dictionaryNodeRelation.Codes != "" {
		bookNumber = dictionaryNodeRelation.Codes
	}
	return bookType, bookNumber, nil
}

// 创建业务字典关系
func (l *CreateBookLogic) createBusinessDictionaryRelation(dictionaryNodeID string, businessID string) error {
	return mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).CreateBusinessDictionaryRelation(l.ctx, mapper.BusinessDictionaryRelation{
		ID:               l.svcCtx.IdGenerator.GenerateIDString(),
		BusinessID:       businessID,
		BusinessType:     consts.BusinessDictionaryBusinessTypeBookType,
		DictionaryNodeID: dictionaryNodeID,
		CreatedAt:        time.Now(),
		CreatedBy:        utils.GetContextUserID(l.ctx),
	})
}
