package document_library

import (
	"context"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDisposalDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDisposalDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDisposalDetailLogic {
	return &GetDisposalDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDisposalDetailLogic) GetDisposalDetail(req *types.GetDisposalDetailReq) (resp *types.GetDisposalDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
