package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeInventoryByIDLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeInventoryByIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeInventoryByIDLogic {
	return &GetDistributeInventoryByIDLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDistributeInventoryByIDLogic) GetDistributeInventoryByID(req *types.GetDistributeInventoryReq) (resp *types.GetDistributeInventoryResp, err error) {
	distributeApplication, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).
		GetDistributeApplicationById(l.ctx, &docvault.GetDistributeApplicationReq{Id: req.ID})
	if err != nil {
		l.Logger.Errorf("获取发放清单信息失败：%v", err)
		return nil, err
	}

	// 数据转换
	distributeInventories := l.dataConversion(distributeApplication.Data)

	return &types.GetDistributeInventoryResp{
		Data: distributeInventories,
	}, nil
}

func (l *GetDistributeInventoryByIDLogic) dataConversion(inventories []*docvault.DistributeInventory) []types.DistributeInventory {
	if len(inventories) == 0 {
		return nil
	}
	var distributeInventories []types.DistributeInventory
	for _, inventory := range inventories {
		eFileLook := types.PermissionResp{
			FileForm:       1,
			FilePermission: 1,
		}
		eFileLookAndDownload := types.PermissionResp{
			FileForm:       1,
			FilePermission: 2,
		}
		paperDocumentOnceDownload := types.PermissionResp{
			FileForm:       2,
			FilePermission: 3,
		}
		eFileOnceDownload := types.PermissionResp{
			FileForm:       1,
			FilePermission: 3,
		}
		// 拆开，加一个字段，告诉前端 内发：1电子文件-查阅 | 内发：2电子文件-查阅/下载 | 内发：3纸质文件-一次下载 | 外发：4电子文件-一次下载
		// 1电子文件 | 2纸质文件
		// 1查阅 | 2查阅/下载 | 3一次下载
		for _, permission := range inventory.Permissions {
			var receivedBy []types.ReceivedBy
			distributeCount := len(receivedBy) // 发放份数
			RecycleCount := 0                  // 回收份数
			DisposalCount := 0                 // 处置份数
			for _, user := range permission.ReceivedBy {
				receivedBy = append(receivedBy, types.ReceivedBy{
					UserID:   user.UserId,
					Nickname: user.Nickname,
					Status:   int(user.Status),
				})
				if user.Status >= 3 {
					RecycleCount++
				}
				if user.Status == 5 {
					DisposalCount++
				}
			}
			if permission.FileForm == 1 && permission.FilePermission == 1 {
				eFileLook.ReceivedBy = receivedBy
			}
			if permission.FileForm == 1 && permission.FilePermission == 2 {
				eFileLookAndDownload.ReceivedBy = receivedBy
			}
			if permission.FileForm == 2 && permission.FilePermission == 3 {
				paperDocumentOnceDownload.ReceivedBy = receivedBy
				paperDocumentOnceDownload.DisposalCount = DisposalCount
				paperDocumentOnceDownload.RecycleCount = RecycleCount
				paperDocumentOnceDownload.DistributeCount = distributeCount
			}
			if permission.FileForm == 1 && permission.FilePermission == 3 {
				eFileOnceDownload.ReceivedBy = receivedBy
			}
		}
		distributeInventories = append(distributeInventories, types.DistributeInventory{
			ID:                        inventory.Id,
			FileId:                    inventory.FileId,
			FileName:                  inventory.FileName,
			Number:                    inventory.Number,
			Version:                   inventory.Version,
			EFileLook:                 eFileLook,
			EFileLookAndDownload:      eFileLookAndDownload,
			PaperDocumentOnceDownload: paperDocumentOnceDownload,
			EFileOnceDownload:         eFileOnceDownload,
		})
	}
	return distributeInventories
}
