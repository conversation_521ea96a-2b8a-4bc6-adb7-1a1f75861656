package document_library

import (
	"context"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDistributeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDistributeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDistributeListLogic {
	return &GetDistributeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDistributeListLogic) GetDistributeList(req *types.GetDistributeListReq) (resp *types.GetDistributeListResp, err error) {
	// TODO 使用 Applicant 模糊搜索用户ids
	var IDs []string
	list, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).GetDistributeInfoList(l.ctx, &docvault.GetDistributeListReq{
		PageInfo: &docvault.PageInfo{
			Page:     int32(req.Page),
			PageSize: int32(req.PageSize),
			NoPage:   req.NoPage,
		},
		FileNumber:     req.FileNumber,
		FileName:       req.FileName,
		FileType:       int32(req.FileType),
		FileCategory:   req.FileCategory,
		DistributeType: int32(req.DistributeType),
		Status:         int32(req.Status),
		Applicant:      IDs,
	})
	if err != nil {
		l.Logger.Errorf("获取发放信息列表失败: %v", err)
		return nil, err
	}

	resp = l.dataTransition(list)
	resp.Total = list.Total

	return resp, nil
}

func (l *GetDistributeListLogic) dataTransition(list *docvault.GetDistributeListResp) *types.GetDistributeListResp {
	var getDistributeListInfo []types.GetDistributeListInfo
	for _, v := range list.Data {
		// 已接收
		var received []types.DistributeUser
		for _, user := range v.Received {
			received = append(received, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		// 未接收
		var notReceived []types.DistributeUser
		for _, user := range v.NotReceived {
			notReceived = append(notReceived, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		// 已回收
		var recycle []types.DistributeUser
		for _, user := range v.Recycle {
			recycle = append(recycle, types.DistributeUser{
				UserID:   user.UserId,
				FileForm: user.FileForm,
				Nickname: user.Nickname,
			})
		}
		approvalInfo := types.ApprovalInfo{}
		if v.ApprovalInfo != nil {
			for _, r := range v.ApprovalInfo.Approvers {
				approvalInfo.Approvers = append(approvalInfo.Approvers, types.Approval{
					UserID:       r.UserId,
					PassedDate:   r.PassedDate,
					UserNickname: r.Nickname,
				})
			}
			for _, r := range v.ApprovalInfo.Auditors {
				approvalInfo.Auditors = append(approvalInfo.Auditors, types.Approval{
					UserID:       r.UserId,
					PassedDate:   r.PassedDate,
					UserNickname: r.Nickname,
				})
			}
		}
		getDistributeListInfo = append(getDistributeListInfo, types.GetDistributeListInfo{
			ID:                 v.Id,
			Applicant:          v.Applicant,
			ApplyDate:          v.ApplyDate,
			DistributeType:     int(v.DistributeType),
			FileType:           int(v.FileType),
			FileCategory:       v.FileCategory,
			Reason:             v.Reason,
			OtherReason:        v.OtherReason,
			WishDistributeDate: v.WishDistributeDate,
			Status:             int(v.Status),
			WorkflowID:         v.WorkflowId,
			ApprovalInfo:       approvalInfo,
			Received:           received,
			NotReceived:        notReceived,
			Recycle:            recycle,
		})
	}
	return &types.GetDistributeListResp{
		Data: getDistributeListInfo,
	}
}
