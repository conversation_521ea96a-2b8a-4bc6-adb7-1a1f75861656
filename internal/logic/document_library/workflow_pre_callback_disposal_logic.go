package document_library

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/utils"

	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type WorkflowPreCallbackDisposalLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewWorkflowPreCallbackDisposalLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WorkflowPreCallbackDisposalLogic {
	return &WorkflowPreCallbackDisposalLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WorkflowPreCallbackDisposalLogic) WorkflowPreCallbackDisposal(req *types.WorkflowInfoReq) (resp *types.WorkflowInfoResp, err error) {
	userLoginInfo := utils.UserLoginInfo{
		UserId:         req.SponsorID,
		TenantId:       req.TenantID,
		OrganizationId: req.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	l.ctx = userLoginInfo.SetContext(l.ctx)

	// 处理表单信息
	var disposalApprovalInfo DisposalApprovalInfo
	if err = json.Unmarshal([]byte(req.FormContent), &disposalApprovalInfo); err != nil {
		l.Logger.Errorf("处理表单信息失败: %v", err)
		return nil, err
	}

	data := disposalApprovalInfo.Data

	updateUserDisposalStatusReq := l.apiReqToRpcReq(data)
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).UpdateUserDisposalStatus(l.ctx, updateUserDisposalStatusReq)
	if err != nil {
		l.Logger.Errorf("预处理回收审批失败: %v", err)
		return nil, err
	}
	return &types.WorkflowInfoResp{}, nil
}

func (l *WorkflowPreCallbackDisposalLogic) apiReqToRpcReq(disposalApprovalInfo DisposalApprovalInfoData) *docvault.UpdateUserDisposalStatusReq {
	updateUserDisposalStatusReq := &docvault.UpdateUserDisposalStatusReq{}
	updateUserDisposalStatusReq.DistributeId = disposalApprovalInfo.DistributeID
	var recycles []*docvault.RecycleList
	for _, v := range disposalApprovalInfo.DisposalList {
		var permissions []*docvault.FilePermission
		for _, permission := range v.Permissions {
			permissions = append(permissions, &docvault.FilePermission{
				// 这里是(2)纸质文件-(3)一次下载
				FileForm:       2,
				FilePermission: 3,
				ReceivedBy:     permission.ReceivedBy,
			})

			recycles = append(recycles, &docvault.RecycleList{
				InventoryId: v.InventoryId,
				Permissions: permissions,
			})
		}
	}
	updateUserDisposalStatusReq.Recycles = recycles
	updateUserDisposalStatusReq.DisposalStatus = 4
	return updateUserDisposalStatusReq
}

type DisposalApprovalInfo struct {
	Data DisposalApprovalInfoData `json:"data"`
}

type DisposalApprovalInfoData struct {
	DistributeID   string         `json:"distributeId"`  // 发放信息列表ID
	DisposalDate   int64          `json:"recycleDate"`   // 处置日期
	DisposalReason string         `json:"recycleReason"` // 处置方式
	DisposalList   []DisposalList `json:"recycleList"`   // 处置清单
	WorkflowID     string         `json:"workflowId"`    // 工作流ID
}

type DisposalList struct {
	InventoryId string        `json:"inventoryId"` // 清单列表ID
	Permissions []Permissions `json:"permissions"` // 文件权限
}
