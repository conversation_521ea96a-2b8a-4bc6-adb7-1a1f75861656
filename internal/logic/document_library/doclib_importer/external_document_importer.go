package doclib_importer

import (
	"context"
	"errors"
	"fmt"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/utils"
	"path"
	"strings"
	"time"
)

type ExternalDocumentExcelInfo struct {
	Name              string // 文档名称
	DocType           string // 文档类型
	OriginalDocNumber string // 原文件号
	Domain            string // 所属领域
	PublishDepartment string // 发文部门
	PublishDocNumber  string // 发文号
	PublishDate       int64  // 发文日期
	EffectiveDate     int64  // 实施日期
	Authentication    string // 认证方式
	OriginalNumber    string // 原文件编号
	OriginalVersion   string // 原文件版本
}

// ExternalDocumentImporter 为外部文档实现 Importer 接口。
type ExternalDocumentImporter struct {
	svcCtx *svc.ServiceContext
	ImporterAbility[ExternalDocumentExcelInfo]
	dictionaryRelation   []DictionaryRelation
	fileNameMap          map[string]string
	docTypeMap           map[string]mapper.BusinessDictionaryNodeRelation
	docDomainMap         map[string]mapper.BusinessDictionaryNodeRelation
	docAuthenticationMap map[string]mapper.BusinessDictionaryNodeRelation
}

// NewExternalDocumentImporter 创建一个新的 ExternalDocumentImporter。
func NewExternalDocumentImporter(svcCtx *svc.ServiceContext) *ExternalDocumentImporter {
	return &ExternalDocumentImporter{
		svcCtx:          svcCtx,
		ImporterAbility: NewImporterAbility[ExternalDocumentExcelInfo](svcCtx),
	}
}

// 解析
func (t *ExternalDocumentImporter) Parse(ctx context.Context, req ImportRequest, rows [][]string) ([]ExternalDocumentExcelInfo, error) {
	excelInfos := make([]ExternalDocumentExcelInfo, 0, len(rows))
	for i, row := range rows {
		if len(row) < 8 {
			continue // 跳过无效行
		}
		info := ExternalDocumentExcelInfo{
			Name:              strings.TrimSpace(row[0]),
			DocType:           strings.TrimSpace(row[1]),
			OriginalDocNumber: strings.TrimSpace(row[2]),
			Domain:            strings.TrimSpace(row[3]),
			PublishDepartment: strings.TrimSpace(row[4]),
			PublishDocNumber:  strings.TrimSpace(row[5]),
		}
		publishDate, err := time.Parse("2006/1/2", strings.TrimSpace(row[6]))
		if err != nil {
			return nil, fmt.Errorf("第%d行发布日期解析失败: %v", i+2, err)
		}
		effectiveDate, err := time.Parse("2006/1/2", strings.TrimSpace(row[7]))
		if err != nil {
			return nil, fmt.Errorf("第%d行有效期解析失败: %v", i+2, err)
		}
		info.PublishDate = publishDate.UnixMilli()
		info.EffectiveDate = effectiveDate.UnixMilli()
		if len(row) > 8 {
			info.Authentication = strings.TrimSpace(row[8])
		}
		if len(row) > 9 {
			info.OriginalNumber = strings.TrimSpace(row[9])
		}
		if len(row) > 10 {
			info.OriginalVersion = strings.TrimSpace(row[10])
		}

		excelInfos = append(excelInfos, info)
	}
	return excelInfos, nil
}

// 实现 ImporterAbility 接口
func (t *ExternalDocumentImporter) Validate(ctx context.Context, req ImportRequest, excelInfos []ExternalDocumentExcelInfo) error {

	// 文件名校验
	if err := t.validateFileNames(ctx, req, excelInfos); err != nil {
		return err
	}
	// 分类名校验
	if err := t.validateDocCategoryNames(ctx, req, excelInfos); err != nil {
		return err
	}
	// 领域校验
	if err := t.validateDomains(ctx, req, excelInfos); err != nil {
		return err
	}
	// 认证方式校验
	if err := t.validateAuthentications(ctx, req, excelInfos); err != nil {
		return err
	}
	return nil
}

func (t *ExternalDocumentImporter) Save(ctx context.Context, req ImportRequest, data []ExternalDocumentExcelInfo) error {
	// 查询组织架构 id
	organizationInfo, err := t.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, utils.GetContextOrganizationID(ctx))
	if err != nil {
		return err
	}

	createReqs := t.buildCreateReqs(data, organizationInfo.Code, organizationInfo.Id)
	infos, err := docvault.NewExternalDocumentLibraryClient(t.svcCtx.DocvaultRpcConn).Create(ctx, createReqs)
	if err != nil {
		return err
	}

	// 构建关系
	t.dictionaryRelation = t.buildDictionaryRelation(infos)
	return nil
}

// 文件名校验
func (t *ExternalDocumentImporter) validateFileNames(ctx context.Context, req ImportRequest, excelInfos []ExternalDocumentExcelInfo) error {
	if len(excelInfos) < len(req.ListFileIDs) {
		return errors.New("台账与文件不匹配，提交失败")
	}

	excelInfoNames := make(map[string]string)
	fileNameMap := make(map[string]string)
	for _, v := range excelInfos {
		excelInfoNames[v.Name] = v.Name
	}
	for _, fileID := range req.ListFileIDs {
		fileName := t.svcCtx.QuickNameTranslator.TranslateFileName(ctx, fileID)
		// 去后缀
		fileName = strings.TrimSuffix(fileName, path.Ext(fileName))
		if _, ok := excelInfoNames[fileName]; !ok {
			return errors.New("台账与文件不匹配，提交失败")
		}
		fileNameMap[fileName] = fileID
	}
	t.fileNameMap = fileNameMap
	return nil
}

// 分类名校验
func (t *ExternalDocumentImporter) validateDocCategoryNames(ctx context.Context, req ImportRequest, excelInfos []ExternalDocumentExcelInfo) error {
	types := make([]string, len(excelInfos))
	for i, excelInfo := range excelInfos {
		types[i] = excelInfo.DocType
	}

	count, err := mapper.NewBusinessDictionaryNodeRelationClient(t.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNames(ctx, types, req.TypeDictionaryID)
	if err != nil {
		return err
	}
	docCategoryMap := utils.ExtractSliceFieldToMap(count, func(v mapper.BusinessDictionaryNodeRelation) (string, mapper.BusinessDictionaryNodeRelation) {
		return v.Names, v
	})
	t.docTypeMap = docCategoryMap
	for _, v := range excelInfos {
		_, ok := docCategoryMap[v.DocType]
		if !ok {
			return fmt.Errorf("分类类型%s不存在", v.DocType)
		}
	}
	return nil
}

// 领域校验
func (t *ExternalDocumentImporter) validateDomains(ctx context.Context, req ImportRequest, excelInfos []ExternalDocumentExcelInfo) error {
	domains := make([]string, len(excelInfos))
	for i, excelInfo := range excelInfos {
		domains[i] = excelInfo.Domain
	}

	count, err := mapper.NewBusinessDictionaryNodeRelationClient(t.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNames(ctx, domains, req.DomainDictionaryID)
	if err != nil {
		return err
	}
	domainCategoryMap := utils.ExtractSliceFieldToMap(count, func(v mapper.BusinessDictionaryNodeRelation) (string, mapper.BusinessDictionaryNodeRelation) {
		return v.Names, v
	})
	t.docDomainMap = domainCategoryMap
	for _, v := range excelInfos {
		_, ok := domainCategoryMap[v.Domain]
		if !ok {
			return fmt.Errorf("领域类型%s不存在", v.Domain)
		}
	}
	return nil
}

// 认证方式校验
func (t *ExternalDocumentImporter) validateAuthentications(ctx context.Context, req ImportRequest, excelInfos []ExternalDocumentExcelInfo) error {
	authentications := make([]string, 0)
	for _, excelInfo := range excelInfos {
		if excelInfo.Authentication == "" {
			continue
		}
		newAuths := strings.Split(excelInfo.Authentication, "、")
		authentications = append(authentications, newAuths...)

	}

	count, err := mapper.NewBusinessDictionaryNodeRelationClient(t.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNames(ctx, authentications, req.AuthDictionaryID)
	if err != nil {
		return err
	}
	authenticationCategoryMap := utils.ExtractSliceFieldToMap(count, func(v mapper.BusinessDictionaryNodeRelation) (string, mapper.BusinessDictionaryNodeRelation) {
		return v.Names, v
	})
	t.docAuthenticationMap = authenticationCategoryMap
	for _, v := range authentications {
		_, ok := authenticationCategoryMap[v]
		if !ok {
			return fmt.Errorf("认证类型%s不存在", v)
		}
	}
	return nil
}

func (t *ExternalDocumentImporter) buildCreateReqs(data []ExternalDocumentExcelInfo, organizationCode string, orgID string) *docvault.ExternalDocumentCreateReq {
	createReqs := make([]*docvault.ExternalDocumentCreateInfo, len(data))
	for i, v := range data {
		auths := strings.Split(v.Authentication, "、")
		authIds := utils.ExtractSliceField(auths, func(auth string) string {
			return t.docAuthenticationMap[auth].NodeID
		})
		code := organizationCode + "/" + t.docTypeMap[v.DocType].Codes
		if t.docTypeMap[v.DocType].Codes != "" {
			code += "-"
		}
		createReqs[i] = &docvault.ExternalDocumentCreateInfo{
			TypeDictionaryNodeId:            t.docTypeMap[v.DocType].NodeID,
			DomainDictionaryNodeId:          t.docDomainMap[v.Domain].NodeID,
			AuthenticationDictionaryNodeIds: authIds,
			NumberPrefix:                    code,
			DocType:                         v.DocType,
			Domain:                          v.Domain,
			Authentications:                 auths,
			Name:                            v.Name,
			OriginalDocNumber:               v.OriginalDocNumber,
			PublishDocNumber:                v.PublishDocNumber,
			PublishDepartment:               v.PublishDepartment,
			FileId:                          t.fileNameMap[v.Name],
			PublishDate:                     v.PublishDate,
			EffectiveDate:                   v.EffectiveDate,
			OriginalNumber:                  v.OriginalNumber,
			OriginalVersion:                 v.OriginalVersion,
		}
	}
	return &docvault.ExternalDocumentCreateReq{
		Data:  createReqs,
		OrgId: orgID,
	}
}

func (*ExternalDocumentImporter) buildDictionaryRelation(infos *docvault.ExternalDocumentCreateResp) []DictionaryRelation {
	dictionaryRelations := make([]DictionaryRelation, len(infos.Data))
	for _, v := range infos.Data {
		dictionaryRelations = append(dictionaryRelations, DictionaryRelation{
			DictionaryNodeID: v.TypeDictionaryNodeId,
			BusinessID:       v.Id,
			BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentCategory,
		})
		dictionaryRelations = append(dictionaryRelations, DictionaryRelation{
			DictionaryNodeID: v.DomainDictionaryNodeId,
			BusinessID:       v.Id,
			BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentDomain,
		})
		for _, auth := range v.AuthenticationDictionaryNodeIds {
			dictionaryRelations = append(dictionaryRelations, DictionaryRelation{
				DictionaryNodeID: auth,
				BusinessID:       v.Id,
				BusinessType:     consts.BusinessDictionaryBusinessTypeExternalDocumentAuthentication,
			})
		}
	}
	return dictionaryRelations
}
