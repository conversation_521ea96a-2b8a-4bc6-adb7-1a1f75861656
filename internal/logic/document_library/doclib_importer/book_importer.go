package doclib_importer

import (
	"context"
	"errors"
	"fmt"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/utils"
	"path"
	"strings"

	"github.com/zeromicro/go-zero/core/logc"
)

type BookExcelInfo struct {
	Name          string
	Author        string
	Publisher     string
	RegisterCount int
	BookType      string
	FileID        string
}

// BookImporter 为书籍实现 Importer 接口。
type BookImporter struct {
	svcCtx *svc.ServiceContext
	ImporterAbility[BookExcelInfo]
	dictionaryRelation []DictionaryRelation
	fileNameMap        map[string]string
	fileTypeMap        map[string]mapper.BusinessDictionaryNodeRelation
}

// NewBookImporter 创建一个新的 BookImporter。
func NewBookImporter(svcCtx *svc.ServiceContext) *BookImporter {
	return &BookImporter{
		svcCtx:          svcCtx,
		ImporterAbility: NewImporterAbility[BookExcelInfo](svcCtx),
	}
}

// 解析
func (t *BookImporter) Parse(ctx context.Context, req ImportRequest, rows [][]string) ([]BookExcelInfo, error) {
	excelInfos := make([]BookExcelInfo, 0, len(rows))
	for _, row := range rows {
		if len(row) < 5 {
			continue // 跳过无效行
		}
		info := BookExcelInfo{
			Name:          strings.TrimSpace(row[0]),
			Author:        strings.TrimSpace(row[1]),
			Publisher:     strings.TrimSpace(row[2]),
			RegisterCount: 0,
			BookType:      strings.TrimSpace(row[4]),
		}
		if row[3] != "" {
			fmt.Sscanf(row[3], "%d", &info.RegisterCount)
		}
		excelInfos = append(excelInfos, info)
	}
	return excelInfos, nil
}

// 实现 ImporterAbility 接口
func (t *BookImporter) Validate(ctx context.Context, req ImportRequest, excelInfos []BookExcelInfo) error {
	if err := t.validateFileNames(ctx, req, excelInfos); err != nil {
		return err
	}
	if err := t.validateBookTypes(ctx, req, excelInfos); err != nil {
		return err
	}
	return nil
}

func (t *BookImporter) Save(ctx context.Context, req ImportRequest, data []BookExcelInfo) error {
	organizationInfo, err := t.svcCtx.PhoenixClient.GetOrganizationInfo(ctx, utils.GetContextOrganizationID(ctx))
	if err != nil {
		return err
	}

	importBookInfo := make([]*docvault.ImportBookInfo, len(data))

	for i, v := range data {
		importBookInfo[i] = &docvault.ImportBookInfo{
			DictionaryNodeId: t.fileTypeMap[v.BookType].NodeID,
			Name:             v.Name,
			Author:           v.Author,
			Publisher:        v.Publisher,
			RegisterCount:    int32(v.RegisterCount),
			BookType:         v.BookType,
			Number:           fmt.Sprintf("%s/%s-", organizationInfo.Code, t.fileTypeMap[v.BookType].Codes),
			FileId:           t.fileNameMap[v.Name],
		}
	}

	importBookResp, err := docvault.NewBookClient(t.svcCtx.DocvaultRpcConn).ImportBook(ctx, &docvault.ImportBookReq{
		ImportBookInfo: importBookInfo,
	})
	if err != nil {
		logc.Errorf(ctx, "导入书籍信息失败：%v", err)
		return err
	}

	t.dictionaryRelation = make([]DictionaryRelation, len(data))
	for i, v := range importBookResp.Data {
		t.dictionaryRelation[i] = DictionaryRelation{
			DictionaryNodeID: v.DictionaryNodeId,
			BusinessID:       v.BookId,
			BusinessType:     consts.BusinessDictionaryBusinessTypeBookType,
		}
	}

	return nil
}

func (t *BookImporter) GetDictionaryRelation(ctx context.Context, req ImportRequest) ([]DictionaryRelation, error) {
	return t.dictionaryRelation, nil
}

func (t *BookImporter) validateFileNames(ctx context.Context, req ImportRequest, excelInfos []BookExcelInfo) error {
	if len(excelInfos) < len(req.ListFileIDs) {
		return errors.New("台账与文件不匹配，提交失败")
	}

	excelInfoNames := make(map[string]struct{})
	t.fileNameMap = make(map[string]string)
	for _, v := range excelInfos {
		excelInfoNames[v.Name] = struct{}{}
	}
	for _, fileID := range req.ListFileIDs {
		fileName := t.svcCtx.QuickNameTranslator.TranslateFileName(ctx, fileID)
		// 去后缀
		fileName = strings.TrimSuffix(fileName, path.Ext(fileName))
		t.fileNameMap[fileName] = fileID
		if _, ok := excelInfoNames[fileName]; !ok {
			return errors.New("台账与文件不匹配，提交失败")
		}
	}

	return nil
}

// 书籍类型校验
func (t *BookImporter) validateBookTypes(ctx context.Context, req ImportRequest, excelInfos []BookExcelInfo) error {
	types := make([]string, len(excelInfos))
	for i, excelInfo := range excelInfos {
		types[i] = excelInfo.BookType
	}

	ts, err := mapper.NewBusinessDictionaryNodeRelationClient(t.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNames(ctx, types, req.TypeDictionaryID)
	if err != nil {
		return err
	}

	t.fileTypeMap = utils.ExtractSliceFieldToMap(ts, func(v mapper.BusinessDictionaryNodeRelation) (string, mapper.BusinessDictionaryNodeRelation) {
		return v.Names, v
	})

	for _, v := range excelInfos {
		if _, ok := t.fileTypeMap[v.BookType]; !ok {
			return fmt.Errorf("书籍类型%s不存在", v.BookType)
		}
	}
	return nil
}
