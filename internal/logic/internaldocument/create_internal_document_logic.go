package internaldocument

import (
	"context"
	"fmt"
	"time"

	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateInternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateInternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateInternalDocumentLogic {
	return &CreateInternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateInternalDocumentLogic) CreateInternalDocument(req *types.CreateInternalDocumentReq) (resp *types.CreateInternalDocumentResp, err error) {
	// 查询文件编号
	noPrefix, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeID(l.ctx, req.DocCategoryID)
	if err != nil {
		l.Logger.Errorf("GetBusinessDictionaryNodeRelationByNodeID error: %v", err)
		return
	}

	// 查询组织架构信息
	organizationInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, utils.GetContextOrganizationID(l.ctx))
	if err != nil {
		l.Logger.Errorf("GetOrganizationInfo error: %v", err)
		return
	}

	rs, err := docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Create(
		l.ctx,
		&docvault.InternalDocumentCreateReq{
			NoPrefix:          fmt.Sprintf("%s/%s", organizationInfo.Code, noPrefix.Codes),
			Name:              req.Name,
			FileId:            req.FileID,
			DocCategoryId:     req.DocCategoryID,
			DepartmentId:      req.DepartmentID,
			PublishDate:       req.PublishDate,
			EffectiveDate:     req.EffectiveDate,
			OriginalNo:        req.OriginalNo,
			OriginalVersionNo: req.OriginalVersionNo,
			AuthorId:          req.AuthorID,
		},
	)
	if err != nil {
		return
	}

	err = l.createBusinessDictionaryRelation(req, rs.Id)

	return
}

// 创建业务字典关系
func (l *CreateInternalDocumentLogic) createBusinessDictionaryRelation(req *types.CreateInternalDocumentReq, businessID string) error {
	return mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).CreateBusinessDictionaryRelation(l.ctx, mapper.BusinessDictionaryRelation{
		ID:               l.svcCtx.IdGenerator.GenerateIDString(),
		BusinessID:       businessID,
		BusinessType:     consts.BusinessDictionaryBusinessTypeInternalDocumentCategory,
		DictionaryNodeID: req.DocCategoryID,
		CreatedAt:        time.Now(),
		CreatedBy:        utils.GetContextUserID(l.ctx),
	})
}
