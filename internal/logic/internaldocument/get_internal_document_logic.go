package internaldocument

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDocumentLogic {
	return &GetInternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInternalDocumentLogic) GetInternalDocument(req *types.GetInternalDocumentReq) (resp *types.GetInternalDocumentResp, err error) {

	rs, err := docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Get(
		l.ctx,
		&docvault.InternalDocumentGetReq{
			Id: req.ID,
		},
	)
	if err != nil {
		return
	}

	auditors, approvers := l.buildApprovalInfo(rs)

	resp = &types.GetInternalDocumentResp{
		ID:                rs.Id,
		No:                rs.No,
		VersionNo:         rs.VersionNo,
		OriginalNo:        rs.OriginalNo,
		OriginalVersionNo: rs.OriginalVersionNo,
		Name:              rs.Name,
		DocCategoryID:     rs.DocCategoryId,
		DepartmentID:      rs.DepartmentId,
		AuthorID:          rs.AuthorId,
		AuthorNickname:    l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, rs.AuthorId),
		PublishDate:       rs.PublishDate,
		EffectiveDate:     rs.EffectiveDate,
		Status:            int(rs.Status),
		FileID:            rs.FileId,
		ApprovalInfo: types.ApprovalInfo{
			Auditors:  auditors,
			Approvers: approvers,
		},
	}

	return
}

func (l *GetInternalDocumentLogic) buildApprovalInfo(rs *docvault.InternalDocumentGetResp) ([]types.Approval, []types.Approval) {
	auditors := make([]types.Approval, 0)
	for _, auditor := range rs.ApprovalInfo.Auditors {
		auditors = append(auditors, types.Approval{
			UserID:       auditor.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, auditor.UserId),
			PassedDate:   auditor.PassedDate,
		})
	}

	approvers := make([]types.Approval, 0)
	for _, approver := range rs.ApprovalInfo.Approvers {
		approvers = append(approvers, types.Approval{
			UserID:       approver.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, approver.UserId),
			PassedDate:   approver.PassedDate,
		})
	}
	return auditors, approvers
}
