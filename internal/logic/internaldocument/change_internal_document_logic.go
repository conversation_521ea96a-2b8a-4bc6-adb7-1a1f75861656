package internaldocument

import (
	"context"
	"fmt"

	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type ChangeInternalDocumentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewChangeInternalDocumentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ChangeInternalDocumentLogic {
	return &ChangeInternalDocumentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ChangeInternalDocumentLogic) ChangeInternalDocument(req *types.ChangeInternalDocumentReq) (resp *types.ChangeInternalDocumentResp, err error) {
	// 查询文件编号
	noPrefix, err := mapper.NewBusinessDictionaryNodeRelationClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeRelationByNodeID(l.ctx, req.DocCategoryID)
	if err != nil {
		l.Logger.Errorf("GetBusinessDictionaryNodeRelationByNodeID error: %v", err)
		return
	}

	// 查询组织架构信息
	organizationInfo, err := l.svcCtx.PhoenixClient.GetOrganizationInfo(l.ctx, utils.GetContextOrganizationID(l.ctx))
	if err != nil {
		l.Logger.Errorf("GetOrganizationInfo error: %v", err)
		return
	}

	_, err = docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Change(
		l.ctx,
		&docvault.InternalDocumentChangeReq{
			Id:                req.ID,
			Name:              req.Name,
			FileId:            req.FileID,
			DocCategoryId:     req.DocCategoryID,
			DepartmentId:      req.DepartmentID,
			PublishDate:       req.PublishDate,
			EffectiveDate:     req.EffectiveDate,
			OriginalNo:        req.OriginalNo,
			OriginalVersionNo: req.OriginalVersionNo,
			NoPrefix:          fmt.Sprintf("%s/%s", organizationInfo.Code, noPrefix.Codes),
			AuthorId:          req.AuthorID,
		},
	)
	if err != nil {
		return
	}

	//
	err = mapper.NewBusinessDictionaryRelationClient(l.svcCtx.NebulaDB).UpdateBusinessDictionaryRelationByBusinessID(l.ctx, req.ID, consts.BusinessDictionaryBusinessTypeInternalDocumentCategory, req.DocCategoryID)
	return
}
