package internaldocument

import (
	"context"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInternalDocumentsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInternalDocumentsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInternalDocumentsLogic {
	return &GetInternalDocumentsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInternalDocumentsLogic) GetInternalDocuments(req *types.GetInternalDocumentsReq) (resp *types.GetInternalDocumentsResp, err error) {

	rs, err := docvault.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).Page(
		l.ctx,
		&docvault.InternalDocumentPageReq{
			PageInfo: &docvault.PageInfo{
				Page:     int32(req.PageInfo.Page),
				PageSize: int32(req.PageInfo.PageSize),
				NoPage:   req.PageInfo.NoPage,
			},
			DocCategoryIds: req.DocCategoryIDs,
			DepartmentIds:  req.DepartmentIDs,
			Status:         int32(req.Status),
			HasAttachment:  int32(req.HasAttachment),
			No:             req.No,
			OriginalNo:     req.OriginalNo,
			Name:           req.Name,
		},
	)
	if err != nil {
		return
	}

	data := l.buildData(rs)

	resp = &types.GetInternalDocumentsResp{
		PageInfo: types.PageInfo{
			Total: uint64(rs.Total),
		},
		Data: data,
	}
	return
}

func (l *GetInternalDocumentsLogic) buildData(rs *docvault.InternalDocumentPageResp) []types.InternalDocumentInfo {
	if len(rs.Data) == 0 {
		return []types.InternalDocumentInfo{}
	}
	data := make([]types.InternalDocumentInfo, 0)
	docCategoryIDs := make([]string, 0)
	for _, item := range rs.Data {
		docCategoryIDs = append(docCategoryIDs, item.DocCategoryId)
		auditors, approvers := l.buildApprovalInfo(item)
		data = append(data, types.InternalDocumentInfo{
			ID:                item.Id,
			No:                item.No,
			VersionNo:         item.VersionNo,
			OriginalNo:        item.OriginalNo,
			OriginalVersionNo: item.OriginalVersionNo,
			Name:              item.Name,
			DocCategoryID:     item.DocCategoryId,
			DepartmentID:      item.DepartmentId,
			DepartmentName:    l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, item.DepartmentId),
			AuthorID:          item.AuthorId,
			AuthorNickname:    l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.AuthorId),
			PublishDate:       item.PublishDate,
			EffectiveDate:     item.EffectiveDate,
			Status:            int(item.Status),
			ApprovalInfo: types.ApprovalInfo{
				Auditors:  auditors,
				Approvers: approvers,
			},
		})
	}

	// 查询分类名称
	docCategories, err := mapper.NewBusinessDictionaryNodeClient(l.svcCtx.NebulaDB).GetBusinessDictionaryNodeByIDs(l.ctx, docCategoryIDs)
	if err != nil {
		return data
	}

	docCategoryMap := make(map[string]string)
	for _, docCategory := range docCategories {
		docCategoryMap[docCategory.ID] = docCategory.Name
	}

	for i := range data {
		data[i].DocCategoryName = docCategoryMap[data[i].DocCategoryID]
	}

	return data
}

func (l *GetInternalDocumentsLogic) buildApprovalInfo(rs *docvault.InternalDocumentPageItem) ([]types.Approval, []types.Approval) {
	auditors := make([]types.Approval, 0)
	for _, item := range rs.ApprovalInfo.Auditors {
		auditors = append(auditors, types.Approval{
			UserID:       item.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserId),
			PassedDate:   item.PassedDate,
		})
	}

	approvers := make([]types.Approval, 0)
	for _, item := range rs.ApprovalInfo.Approvers {
		approvers = append(approvers, types.Approval{
			UserID:       item.UserId,
			UserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, item.UserId),
			PassedDate:   item.PassedDate,
		})
	}

	return auditors, approvers
}
