package kqs

import (
	"context"

	"github.com/zeromicro/go-zero/core/logc"
)

// BusinessDictionaryChangeProducerInterface 业务字典变更生产者接口
// 支持批量发送切片数据
//
//go:generate mockgen -source=business_dictionary_change_producer.go -destination=mock_business_dictionary_change_producer.go -package=kqs
type BusinessDictionaryChangeProducerInterface interface {
	SendMessage(ctx context.Context, values []string) error
	Close() error
}

// BusinessDictionaryChangeProducer 专用于业务字典变更的 Kafka 生产者
// 支持批量发送切片数据
type BusinessDictionaryChangeProducer struct {
	KafkaProducer *KafkaProducer
}

func NewBusinessDictionaryChangeProducer(brokers []string, topic string) *BusinessDictionaryChangeProducer {
	return &BusinessDictionaryChangeProducer{
		KafkaProducer: NewKafkaProducer(brokers, topic),
	}
}

// SendMessage 批量发送业务字典变更数据
func (p *BusinessDictionaryChangeProducer) SendMessage(ctx context.Context, values []string) error {
	err := p.KafkaProducer.SendMessage(ctx, "business_dictionary_change", values)
	if err != nil {
		logc.Errorf(ctx, "业务字典变更消息发送失败，err=%v", err)
	}
	return err
}

func (p *BusinessDictionaryChangeProducer) Close() error {
	return p.KafkaProducer.Close()
}
