package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const (
	BusinessDictionaryNodeTableName = "business_dictionary_node"
)

type BusinessDictionaryNodeClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewBusinessDictionaryNodeClient(db *NebulaDB) *BusinessDictionaryNodeClient {
	return &BusinessDictionaryNodeClient{
		DB: db.db,
	}
}

func (c *BusinessDictionaryNodeClient) GetById(ctx context.Context, id string) (BusinessDictionaryNode, error) {
	var node BusinessDictionaryNode
	if err := c.DB.WithContext(ctx).Model(&BusinessDictionaryNode{}).Where("id = ?", id).First(&node).Error; err != nil {
		return BusinessDictionaryNode{}, err
	}
	return node, nil
}

func (c *BusinessDictionaryNodeClient) GetBusinessDictionaryNodesByDictionaryIDAndParentID(ctx context.Context, dictionaryID, parentID string) ([]BusinessDictionaryNode, error) {
	var nodes []BusinessDictionaryNode
	query := c.DB.WithContext(ctx).Model(&BusinessDictionaryNode{}).Where("dictionary_id = ? AND is_deleted = 0", dictionaryID)
	if parentID != "" {
		query = query.Where("parent_id = ?", parentID)
	} else {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	}
	if err := query.Order("sort ASC,created_at DESC").Find(&nodes).Error; err != nil {
		return nil, err
	}
	return nodes, nil
}

// 创建节点
func (c *BusinessDictionaryNodeClient) CreateBusinessDictionaryNode(ctx context.Context, node BusinessDictionaryNode) error {
	return c.DB.WithContext(ctx).Create(&node).Error
}
func (c *BusinessDictionaryNodeClient) SaveBusinessDictionaryNodes(ctx context.Context, tx *NebulaTX, nodes []BusinessDictionaryNode) error {
	return tx.GetTX().Save(&nodes).Error
}

// 更新节点
func (c *BusinessDictionaryNodeClient) UpdateBusinessDictionaryNode(ctx context.Context, tx *NebulaTX, node BusinessDictionaryNode) error {
	return tx.GetTX().Model(&BusinessDictionaryNode{}).Select("*").Where("id = ?", node.ID).Updates(node).Error
}

// 更新使用事务
func (c *BusinessDictionaryNodeClient) UpdateBusinessDictionaryNodeWithTransaction(ctx context.Context, node BusinessDictionaryNode, tx *NebulaTX) error {
	return tx.GetTX().Model(&BusinessDictionaryNode{}).Where("id = ?", node.ID).Updates(node).Error
}

// 删除节点
func (c *BusinessDictionaryNodeClient) DeleteBusinessDictionaryNode(ctx context.Context, tx *NebulaTX, nodeID []string) error {
	return tx.GetTX().Model(&BusinessDictionaryNode{}).Where("id IN (?)", nodeID).Update("is_deleted", true).Error
}

// 查询节点最大序号
func (c *BusinessDictionaryNodeClient) GetBusinessDictionaryNodeMaxSort(ctx context.Context, dictionaryID, parentID string) (int, error) {
	query := c.DB.WithContext(ctx).Model(&BusinessDictionaryNode{}).Where("dictionary_id = ?", dictionaryID)
	if parentID != "" {
		query = query.Where("parent_id = ?", parentID)
	} else {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	}
	var maxSort int
	if err := query.Select("IFNULL(MAX(sort), 0)").Find(&maxSort).Error; err != nil {
		return 0, err
	}

	return maxSort, nil
}

// WITH RECURSIVE sub_nodes AS (
//
//		SELECT *
//		FROM business_dictionary_node
//		WHERE id = ? AND is_deleted = 0
//		UNION ALL
//		SELECT n.*
//		FROM business_dictionary_node n
//		INNER JOIN sub_nodes sn ON n.parent_id = sn.id
//		WHERE n.is_deleted = 0
//	  )
//	  SELECT *
//	  FROM sub_nodes
//	  WHERE id != ?; -- 排除自身，只要下级
//
// 查询下级节点
func (c *BusinessDictionaryNodeClient) GetBusinessDictionaryIDsByParentID(ctx context.Context, parentID string) ([]string, error) {
	var nodes []string
	query := c.DB.WithContext(ctx).Raw("WITH RECURSIVE sub_nodes AS (SELECT * FROM business_dictionary_node WHERE id = ? AND is_deleted = 0 UNION ALL SELECT n.* FROM business_dictionary_node n INNER JOIN sub_nodes sn ON n.parent_id = sn.id WHERE n.is_deleted = 0) SELECT id FROM sub_nodes WHERE id != ?", parentID, parentID)
	if err := query.Scan(&nodes).Error; err != nil {
		return nil, err
	}
	return nodes, nil
}

// 查询同名称节点
func (c *BusinessDictionaryNodeClient) GetBusinessDictionaryNodeCountsByNameOrCode(ctx context.Context, dictionaryID, parentID, name, code string) (int64, error) {
	var count int64
	query := c.DB.WithContext(ctx).Model(&BusinessDictionaryNode{}).Where("dictionary_id = ? AND (name = ? OR code = ? AND Code!='' AND Code IS NOT NULL) AND is_deleted = 0", dictionaryID, name, code)

	if parentID != "" {
		query = query.Where("parent_id = ?", parentID)
	} else {
		query = query.Where("parent_id IS NULL OR parent_id = ''")
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// 根据dictionaryID查询字典节点
func (c *BusinessDictionaryNodeClient) GetBusinessDictionaryNodeByDictionaryId(ctx context.Context, dictionaryID string) (businessDictionaryNodes []BusinessDictionaryNode, err error) {
	err = c.DB.WithContext(ctx).
		Where("dictionary_id = ? and status = 1 and is_deleted = 0", dictionaryID).
		Order("sort asc").
		Order("created_at desc").
		Find(&businessDictionaryNodes).Error
	return businessDictionaryNodes, err
}

func (c *BusinessDictionaryNodeClient) GetBusinessDictionaryNodeByIDs(ctx context.Context, ids []string) (businessDictionaryNodes []BusinessDictionaryNode, err error) {
	err = c.DB.WithContext(ctx).Where("id IN (?)", ids).Find(&businessDictionaryNodes).Error
	return businessDictionaryNodes, err
}

type BusinessDictionaryNode struct {
	ID           string    `gorm:"primaryKey;column:id" json:"id"`
	DictionaryID string    `gorm:"column:dictionary_id" json:"dictionaryId"`
	ParentID     string    `gorm:"column:parent_id" json:"parentId,omitempty"`
	Code         string    `gorm:"column:code" json:"code"`
	Name         string    `gorm:"column:name" json:"name"`
	Remark       string    `gorm:"column:remark" json:"remark,omitempty"`
	Sort         int       `gorm:"column:sort" json:"sort"`
	Status       bool      `gorm:"column:status" json:"status"`
	IsDeleted    bool      `gorm:"column:is_deleted" json:"isDeleted"`
	CreatedAt    time.Time `gorm:"column:created_at" json:"createdAt"`
	CreatedBy    string    `gorm:"column:created_by" json:"createdBy"`
	UpdatedAt    time.Time `gorm:"column:updated_at" json:"updatedAt"`
	UpdatedBy    string    `gorm:"column:updated_by" json:"updatedBy"`
}

func (BusinessDictionaryNode) TableName() string {
	return BusinessDictionaryNodeTableName
}

// 导出方法：获取节点完整父链
func (c *BusinessDictionaryNodeClient) GetNodeAncestry(ctx context.Context, tx *NebulaTX, nodeID string) ([]BusinessDictionaryNode, error) {
	var ancestry []BusinessDictionaryNode
	// MySQL 8.0+ 递归 CTE 查询所有祖先节点（含自身），parent_id 为空兼容 NULL 和 ''
	query := `
		WITH RECURSIVE ancestors AS (
			SELECT *, 0 AS depth FROM business_dictionary_node WHERE id = ? AND is_deleted = 0
			UNION ALL
			SELECT n.*, a.depth + 1 FROM business_dictionary_node n
			INNER JOIN ancestors a ON n.id = a.parent_id
			WHERE n.is_deleted = 0
		)
		SELECT id, dictionary_id, parent_id, code, name, remark, sort, status, is_deleted, created_at, created_by, updated_at, updated_by
		FROM ancestors
		ORDER BY depth DESC
	`
	if err := tx.GetTX().Raw(query, nodeID).Scan(&ancestry).Error; err != nil {
		return nil, err
	}
	// 结果已按 depth DESC 排序（根在前，当前节点在后）
	return ancestry, nil
}
