package workflow

import (
	"context"
	"encoding/json"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logc"
)

type RecycleApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewRecycleApprovalConsumer(svcCtx *svc.ServiceContext) *RecycleApprovalConsumer {
	return &RecycleApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *RecycleApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *RecycleApprovalConsumer) Name() string {
	return "file_reclaim"
}

func (h *RecycleApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 构造用户信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	// 处理表单信息
	var recycleApprovalInfo RecycleApprovalInfo
	if err := json.Unmarshal([]byte(msg.FormContent), &recycleApprovalInfo); err != nil {
		logc.Errorf(ctx, "处理表单信息失败: %v", err)
		return
	}

	data := recycleApprovalInfo.Data
	// 如果审批被拒绝，用户状态改回1
	if msg.EventType == consts.WorkflowEventRejected {
		updateUserDisposalStatusReq := h.apiReqToRpcReq(data)
		_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateUserDisposalStatus(ctx, updateUserDisposalStatusReq)
		if err != nil {
			logc.Errorf(ctx, "预处理回收审批失败: %v", err)
		}
		return
	}

	// 如果审批通过
	recycleApproval := h.reqToRpc(data)
	recycleApproval.WorkflowId = msg.WorkflowID
	// 获取审核人和审批人
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		return
	}
	recycleApproval.ApprovalInfo = approvalInfo
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).SaveRecycleApprovalInfo(ctx, recycleApproval)
	if err != nil {
		logc.Errorf(ctx, "处理回收审批失败: %v", err)
	}
}

func (h *RecycleApprovalConsumer) reqToRpc(data RecycleApprovalInfoData) *docvault.RecycleApprovalInfo {
	recycleApprovalInfo := &docvault.RecycleApprovalInfo{}

	// 基本字段转换
	recycleApprovalInfo.DistributeId = data.DistributeID
	recycleApprovalInfo.RecycleDate = data.RecycleDate
	recycleApprovalInfo.RecycleReason = data.RecycleReason
	recycleApprovalInfo.OtherReason = data.OtherReason
	recycleApprovalInfo.WorkflowId = data.WorkflowID

	// 转换 RecycleList 数组
	var recycleList []*docvault.RecycleList
	for _, item := range data.RecycleList {
		recycleItem := &docvault.RecycleList{
			InventoryId: item.InventoryId,
		}

		// 转换 Permissions 数组
		var permissions []*docvault.FilePermission
		for _, permission := range item.Permissions {
			filePermission := &docvault.FilePermission{
				FileForm:       int32(permission.FileForm),
				FilePermission: int32(permission.FilePermission),
				ReceivedBy:     permission.ReceivedBy,
			}
			permissions = append(permissions, filePermission)
		}
		recycleItem.Permissions = permissions
		recycleList = append(recycleList, recycleItem)
	}
	recycleApprovalInfo.RecycleList = recycleList

	return recycleApprovalInfo
}

func (h *RecycleApprovalConsumer) apiReqToRpcReq(recycleApprovalInfo RecycleApprovalInfoData) *docvault.UpdateUserDisposalStatusReq {
	updateUserDisposalStatusReq := &docvault.UpdateUserDisposalStatusReq{}
	updateUserDisposalStatusReq.DistributeId = recycleApprovalInfo.DistributeID
	var recycles []*docvault.RecycleList
	for _, v := range recycleApprovalInfo.RecycleList {
		var permissions []*docvault.FilePermission
		for _, permission := range v.Permissions {
			permissions = append(permissions, &docvault.FilePermission{
				FileForm:       int32(permission.FileForm),
				FilePermission: int32(permission.FilePermission),
				ReceivedBy:     permission.ReceivedBy,
			})

			recycles = append(recycles, &docvault.RecycleList{
				InventoryId: v.InventoryId,
				Permissions: permissions,
			})
		}
	}
	updateUserDisposalStatusReq.Recycles = recycles
	updateUserDisposalStatusReq.DisposalStatus = 1
	return updateUserDisposalStatusReq
}

type RecycleApprovalInfo struct {
	Data RecycleApprovalInfoData `json:"data"`
}

type RecycleApprovalInfoData struct {
	DistributeID  string        `json:"distributeId"`  // 发放列表ID
	RecycleDate   int64         `json:"recycleDate"`   // 回收日期
	RecycleReason string        `json:"recycleReason"` // 回收原因
	OtherReason   string        `json:"otherReason"`   // 其他原因
	RecycleList   []RecycleList `json:"recycleList"`   // 回收清单
	WorkflowID    string        `json:"workflowId"`    // 工作流ID
}

type RecycleList struct {
	InventoryId string       `json:"inventoryId"` // 清单列表ID
	Permissions []Permission `json:"permissions"` // 文件权限
}

type Permission struct {
	FileForm       int      `json:"fileForm"`       // 文件形式
	FilePermission int      `json:"filePermission"` // 文件权限
	ReceivedBy     []string `json:"receivedBy"`     // 接收人
}
