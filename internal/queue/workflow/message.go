package workflow

type WorkflowEventMessage struct {
	TenantID       string `json:"tenant_id"`       // 租户ID
	OrganizationID string `json:"organization_id"` // 组织架构ID
	WorkflowID     string `json:"workflow_id"`     // 工作流ID
	SponsorID      string `json:"sponsor_id"`      // 发起人ID
	FormContent    string `json:"form_content"`    // 审批表单信息（JSON字符串）
	CompletedAt    int64  `json:"completed_at"`    // 完成时间（毫秒级时间戳）
	CreatedAt      int64  `json:"created_at"`      // 发起时间（毫秒级时间戳）
	BusinessID     string `json:"business_id"`     // 流程序列号
	BusinessCode   string `json:"business_code"`   // 业务代码
	EventType      string `json:"event_type"`      // 事件类型
}
