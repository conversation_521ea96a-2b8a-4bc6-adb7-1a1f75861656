package workflow

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/utils"
)

type DistributeApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewDistributeApprovalConsumer(svcCtx *svc.ServiceContext) *DistributeApprovalConsumer {
	return &DistributeApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *DistributeApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleApproved(ctx, msg)
	return nil
}

func (h *DistributeApprovalConsumer) Name() string {
	return "file_grant"
}

func (h *DistributeApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 审批驳回，更行状态为已驳回
	if msg.EventType == consts.WorkflowEventRejected {
		_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateDistributeStatusByWorkflowId(ctx, &docvault.UpdateDistributeStatusReq{
			WorkflowId: msg.WorkflowID,
			Status:     4,
		})
		if err != nil {
			logc.Errorf(ctx, "处理审批失败: %v", err)
			return
		}
		return
	}
	// 审批撤销，更新状态为待提交
	if msg.EventType == consts.WorkflowEventCanceled {
		_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateDistributeStatusByWorkflowId(ctx, &docvault.UpdateDistributeStatusReq{
			WorkflowId: msg.WorkflowID,
			Status:     1,
		})
		if err != nil {
			logc.Errorf(ctx, "处理审批失败: %v", err)
			return
		}
		return
	}

	// 如果审批通过，保存审批人信息
	// 获取审核人和审批人
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		return
	}

	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).SaveDistributeApproval(ctx, &docvault.DistributeApprovalReq{
		WorkflowId:   msg.WorkflowID,
		Status:       3,
		ApprovalInfo: approvalInfo,
	})
	if err != nil {
		logc.Errorf(ctx, "处理审批失败: %v", err)
	}
}

func GetApproval(ctx context.Context, svcCtx *svc.ServiceContext, msg WorkflowEventMessage) (*docvault.ApprovalInfo, error) {
	// 构造用户洗信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	approveWorkflow, err := svcCtx.PhoenixClient.GetWorkflow(ctx, msg.WorkflowID)
	if err != nil {
		logc.Errorf(ctx, "获取审批流程信息失败: %v", err)
		return nil, err
	}
	approvalInfo := &docvault.ApprovalInfo{}
	if len(approveWorkflow.Nodes) == 0 {
		return approvalInfo, nil
	}
	// 如果节点长度为1，说明审核人和审批人是一样的
	if len(approveWorkflow.Nodes) == 1 {
		for _, approver := range approveWorkflow.Nodes[0].Approvers {
			approvalInfo.Auditors = append(approvalInfo.Auditors, &docvault.ApprovalInfoItem{
				UserId:     approver.ApproverID,
				PassedDate: approver.UpdatedAt,
			})
			approvalInfo.Approvers = append(approvalInfo.Approvers, &docvault.ApprovalInfoItem{
				UserId:     approver.ApproverID,
				PassedDate: approver.UpdatedAt,
			})
		}
		return approvalInfo, nil
	}
	// 节点长度为2，则第一个是审核人，第二个是审批人
	for _, approver := range approveWorkflow.Nodes[0].Approvers {
		approvalInfo.Auditors = append(approvalInfo.Auditors, &docvault.ApprovalInfoItem{
			UserId:     approver.ApproverID,
			PassedDate: approver.UpdatedAt,
		})
	}
	for _, approver := range approveWorkflow.Nodes[1].Approvers {
		approvalInfo.Approvers = append(approvalInfo.Approvers, &docvault.ApprovalInfoItem{
			UserId:     approver.ApproverID,
			PassedDate: approver.UpdatedAt,
		})
	}
	return approvalInfo, nil
}
