package aggregate

import (
	"context"
	"encoding/json"
	"errors"
	"nebula/internal/domain/entity"
	"nebula/internal/domain/value"
	"nebula/internal/utils"
	"sort"
	"time"
)

type Signature struct {
	BasicAbilityReference
	SignatureReference
}

func NewSignature(basicAbilityReference BasicAbilityReference, signatureReference SignatureReference) SignatureService {
	return &Signature{
		BasicAbilityReference: basicAbilityReference,
		SignatureReference:    signatureReference,
	}
}

func (s *Signature) CreateSignatureTask(ctx context.Context) (taskId string, err error) {
	taskId = s.BasicAbilityReference.GenerateID()
	if err := s.SignatureReference.CreateSignatureTask(ctx, taskId); err != nil {
		return "", err
	}

	return taskId, nil
}

func (s *Signature) UploadSignature(ctx context.Context, taskId string, signatureBase64 string) (err error) {
	task, err := s.SignatureReference.GetSignatureTask(ctx, taskId)
	if err != nil {
		return err
	}
	if task.SignatureBase64 != "" {
		return errors.New("任务已签名，请勿重复上传")
	}

	if err := s.SignatureReference.UpdateSignatureTask(ctx, taskId, signatureBase64); err != nil {
		return err
	}

	return nil
}

func (s *Signature) ApprovalCompleted(ctx context.Context, msg ApprovalResultMsg) (err error) {
	// 获取审批流信息
	// 构造用户洗信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	workflowInfo, err := s.BasicAbilityReference.GetWorkflowInfo(ctx, msg.WorkflowID)
	if err != nil {
		return err
	}

	var formContent value.SignatureWorkflowFormContent
	if err := json.Unmarshal([]byte(msg.FormContent), &formContent); err != nil {
		return err
	}
	authLetterFileName := s.BasicAbilityReference.TranslateUserNickname(ctx, msg.SponsorID) + "签名授权书" + time.UnixMilli(msg.CompletedAt).Format(time.DateOnly)
	// 获取最后一个审批人和倒数第二个审批人
	lastApproverID, approverID, approverDate := s.getApproverInfo(workflowInfo)
	authLetterInfo := s.buildAuthLetterParams(ctx, msg, lastApproverID, approverID, approverDate, authLetterFileName, formContent.Data.SignatureBase64)

	authLetterFileID, err := s.SignatureReference.GenerateAuthLetterFile(ctx, authLetterInfo)
	if err != nil {
		return err
	}

	err = s.SignatureReference.SaveSignature(ctx, entity.Signature{
		ID:                 s.BasicAbilityReference.GenerateID(),
		CreatedBy:          msg.SponsorID,
		UpdatedBy:          msg.SponsorID,
		UserID:             msg.SponsorID,
		SignatureBase64:    formContent.Data.SignatureBase64,
		LastApproverID:     lastApproverID,
		AuthLetterFileID:   authLetterFileID,
		AuthLetterFileName: authLetterFileName + ".pdf",
		CreatedAt:          msg.CreatedAt,
		UpdatedAt:          msg.CompletedAt,
		Status:             true,
	})
	return err
}

func (s *Signature) getApproverInfo(progress value.WorkflowInfo) (lastApproverID string, approverID string, approverDate int64) {
	lastNodeApproverTasks := progress.Nodes[len(progress.Nodes)-1].Approvers
	sort.Slice(lastNodeApproverTasks, func(i, j int) bool {
		// 按照更新时间排序 从大到小
		return lastNodeApproverTasks[i].UpdatedAt > lastNodeApproverTasks[j].UpdatedAt
	})

	if len(lastNodeApproverTasks) > 0 {
		lastApproverID = lastNodeApproverTasks[len(lastNodeApproverTasks)-1].ApproverID
	}
	if len(lastNodeApproverTasks) > 1 {
		approverID = lastNodeApproverTasks[len(lastNodeApproverTasks)-2].ApproverID
		approverDate = lastNodeApproverTasks[len(lastNodeApproverTasks)-2].UpdatedAt
	}

	return lastApproverID, approverID, approverDate
}

func (s *Signature) buildAuthLetterParams(ctx context.Context, msg ApprovalResultMsg, lastApproverID string, approverID string, approverDate int64, authLetterFileName string, signatureBase64 string) (authLetterInfo value.AuthLetterInfo) {
	pa := value.AuthLetterInfo{
		AuthLetterFileName: authLetterFileName,
		AuthLetterParams: []value.AuthLetterParam{
			{
				FieldKey:         value.AuthLetterFieldKeySignatureImage,
				FieldValue:       signatureBase64,
				FieldFillingType: value.AuthLetterFieldFillingTypeBase64,
				FieldType:        value.AuthLetterFieldTypeImage,
				ImgWidth:         83,
				ImgHeight:        42,
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApplicant,
				FieldValue: s.BasicAbilityReference.TranslateUserNickname(ctx, msg.SponsorID),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApprover,
				FieldValue: s.BasicAbilityReference.TranslateUserNickname(ctx, approverID),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyLastApprover,
				FieldValue: s.BasicAbilityReference.TranslateUserNickname(ctx, lastApproverID),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApplicantYear,
				FieldValue: time.UnixMilli(msg.CreatedAt).Year(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApplicantMonth,
				FieldValue: time.UnixMilli(msg.CreatedAt).Month(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApplicantDay,
				FieldValue: time.UnixMilli(msg.CreatedAt).Day(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyEnableYear,
				FieldValue: time.UnixMilli(msg.CompletedAt).Year(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyEnableMonth,
				FieldValue: time.UnixMilli(msg.CompletedAt).Month(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyEnableDay,
				FieldValue: time.UnixMilli(msg.CompletedAt).Day(),
			},
		},
	}

	if approverDate > 0 { // 如果审批人存在，则添加审批人信息
		pa.AuthLetterParams = append(pa.AuthLetterParams, []value.AuthLetterParam{
			{
				FieldKey:   value.AuthLetterFieldKeyApproverYear,
				FieldValue: time.UnixMilli(approverDate).Year(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApproverMonth,
				FieldValue: time.UnixMilli(approverDate).Month(),
			},
			{
				FieldKey:   value.AuthLetterFieldKeyApproverDay,
				FieldValue: time.UnixMilli(approverDate).Day(),
			},
		}...)
	}

	return pa
}
