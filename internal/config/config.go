package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	rest.RestConf
	Redis         Redis
	NebulaDB      DBConf
	Snowflake     Snowflake
	MicroServices MicroServices
	Kafka         KafkaConf
	Business      BusinessConf
	DocvaultRPC   zrpc.RpcClientConf `json:",optional"`
}

type Redis struct {
	Host string
	Pass string
	// Type 运行方式，node：节点，cluster：集群
	Type string
}

// DBConf 数据库配置
type DBConf struct {
	Type            string `json:",optional"`
	Host            string
	Port            string
	User            string
	Password        string
	Schema          string
	MaxIdleConns    int
	ConnMaxLifetime int64
	MaxOpenConns    int
}

type Snowflake struct {
	//节点占位
	NodeBits uint8
	// 步骤占位
	StepBits uint8
	// 元年
	Epoch int64
	// 默认节点
	Node int64
}

type MicroServices map[string]MicroService

func (m MicroServices) GetService(name string) MicroService {
	v, ok := m[name]
	if !ok {
		logx.Errorf("未配置服务：%s\n", name)
	}
	return v
}

type MicroService struct {
	Url        string
	Desc       string
	Parameters map[string]string
}

type Producers map[string]ProducerConf

func (p Producers) GetProducer(name string) *ProducerConf {
	v, ok := p[name]
	if !ok {
		logx.Errorf("未配置生产者：%s\n", name)
		return nil
	}
	return &v

}

type KafkaConf struct {
	Brokers   []string
	MinBytes  int
	MaxBytes  int
	Consumers []QueueConf `json:",optional"`
	Producers Producers   `json:",optional"`
}

type ProducerConf struct {
	Topic string
	Key   string
}

type QueueConf struct {
	Topic   string
	GroupID string
	Key     string
}

type BusinessConf struct {
	SignatureAuthLetterFileID string
}
