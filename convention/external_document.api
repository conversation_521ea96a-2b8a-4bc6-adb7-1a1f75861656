syntax = "v1"

type CreateExternalDocumentReq {
    Name string `json:"name"`
    FileID string `json:"fileId,optional"`
    OriginalNumber string `json:"originalNumber,optional"`
    OriginalVersion string `json:"originalVersion,optional"`
    TypeDictionaryNodeId string `json:"typeDictionaryNodeId"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId"`
    AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds,optional"`
    OriginalDocNumber string `json:"originalDocNumber"`
    PublishDocNumber string  `json:"publishDocNumber"`
    PublishDepartment string `json:"publishDepartment"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OrgType int `json:"orgType"`
}

type CreateExternalDocumentResp {

}



type GetExternalDocumentReq {
    DistributeID string `form:"id"`
}



type ChangeExternalDocumentReq {
    ID string `json:"id"`
    Name string `json:"name"`
    OriginalDocNumber string `json:"originalDocNumber"`
    PublishDocNumber string `json:"publishDocNumber"`
    PublishDepartment string `json:"publishDepartment"`
    TypeDictionaryNodeId string `json:"typeDictionaryNodeId"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId"`
    AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds,optional"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OriginalNumber string `json:"originalNumber,optional"`
    OriginalVersion string `json:"originalVersion,optional"`
    FileId string `json:"fileId,optional"`
    OrgType int `json:"orgType"`
}

type ChangeExternalDocumentResp {

}



type GetExternalDocumentsReq {
    PageInfo
    Number string `json:"number,optional"`
    Name string `json:"name,optional"`
    OriginalNumber string `json:"originalNumber,optional"`
    OriginalDocNumber string `json:"originalDocNumber,optional"`
    PublishDocNumber string `json:"publishDocNumber,optional"`
    PublishDepartment string `json:"publishDepartment,optional"`
    TypeDictionaryNodeIds []string `json:"typeDictionaryNodeIds,optional"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId,optional"`
    AuthenticationDictionaryNodeId string `json:"authenticationDictionaryNodeId,optional"`
    BeAttachedFile int8 `json:"beAttachedFile,optional"`
    Status int `json:"status,optional"`
    OrgType int `json:"orgType,optional"`
}

type GetExternalDocumentsResp {
    PageInfo
    Data []ExternalDocumentInfo `json:"data"`
}

type ExternalDocumentInfo {
    ID string `json:"id"`
    Number string `json:"number"`
    Version string `json:"version"`
    OriginalNumber string `json:"originalNumber"`
    OriginalVersion string `json:"originalVersion"`
    Name string `json:"name"`
    DocType string `json:"docType"`
    Domain string `json:"domain"`
    OriginalDocNumber string `json:"originalDocNumber"`
    PublishDocNumber string `json:"publishDocNumber"`
    PublishDepartment string `json:"publishDepartment"`
    ApprovalInfo ApprovalInfo `json:"approvalInfo"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    Authentication string `json:"authentication"`
    Status int `json:"status"`
    TypeDictionaryNodeId string `json:"typeDictionaryNodeId"`
    DomainDictionaryNodeId string `json:"domainDictionaryNodeId"`
    AuthenticationDictionaryNodeIds []string `json:"authenticationDictionaryNodeIds"`
    FileInfo FileInfo `json:"fileInfo"`
}

type PlagiarismCheckReq {
    Ids []string `json:"ids"`
}

type PlagiarismCheckResp {

}