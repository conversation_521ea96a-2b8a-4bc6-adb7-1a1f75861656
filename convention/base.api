syntax = "v1"

info(
    title: "base"
    desc: "base"
)


// The page request parameters | 列表请求参数
type PageInfo {
    // Page number | 第几页
    // Required: true
    // in: query
    Page uint64 `json:"page,optional" validate:"number" form:"page,optional"`

    // Page size | 单页数据行数
    // Required: true
    // Maximum: 100000
    // in: query
    PageSize uint64 `json:"pageSize,optional" validate:"number,max=100000" form:"pageSize,optional"`

    Total uint64 `json:"total,optional"`
    // 是否不分页，默认分页
    NoPage bool `json:"noPage,optional" form:"noPage,optional"`
}

// CommonSearchInfo 搜索
type CommonSearchInfo {
    //    Search | 搜索
    Search string `json:"search,optional" form:"search,optional"`
}




