syntax = "v1"


type CreateInternalDocumentReq {
    Name string `json:"name"`
    FileID string `json:"fileId,optional"`
    DocCategoryID string `json:"docCategoryId"`
    DepartmentID string `json:"departmentId"`
    AuthorID string `json:"authorId"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OriginalNo string `json:"originalNo,optional"`
    OriginalVersionNo string `json:"originalVersionNo,optional"`
}

type CreateInternalDocumentResp {

}



type GetInternalDocumentReq {
    ID string `form:"id"`
}

type GetInternalDocumentResp {
    ID string `json:"id"`
    No string `json:"no"`
    VersionNo string `json:"versionNo"`
    OriginalNo string `json:"originalNo"`
    OriginalVersionNo string `json:"originalVersionNo"`
    Name string `json:"name"`
    DocCategoryID string `json:"docCategoryId"`
    DepartmentID string `json:"departmentId"`
    AuthorID string `json:"authorId"`
    AuthorNickname string `json:"authorNickname"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    Status int `json:"status"`
    ApprovalInfo ApprovalInfo `json:"approvalInfo"`
    FileID string `json:"fileId"`
}

type ApprovalInfo {
    Auditors []Approval `json:"auditors"`
    Approvers []Approval `json:"approvers"`
}

type Approval {
    UserID string `json:"userId"`
    UserNickname string `json:"userNickname"`
    PassedDate int64 `json:"passedDate"`
}




type ChangeInternalDocumentReq {
    ID string `json:"id"`
    Name string `json:"name"`
    FileID string `json:"fileId,optional"`
    DocCategoryID string `json:"docCategoryId"`
    DepartmentID string `json:"departmentId"`
    AuthorID string `json:"authorId"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    OriginalNo string `json:"originalNo,optional"`
    OriginalVersionNo string `json:"originalVersionNo,optional"`
}

type ChangeInternalDocumentResp {

}



type GetInternalDocumentsReq {
    PageInfo
    DocCategoryIDs []string `json:"docCategoryIds,optional"`
    DepartmentIDs []string `json:"departmentIds,optional"`
    Status int `json:"status,optional"` 
     // 是否有附件
    HasAttachment int8 `json:"hasAttachment,optional"`
    Name string `json:"name,optional"`
    No string `json:"no,optional"`
    OriginalNo string `json:"originalNo,optional"`
}

type GetInternalDocumentsResp {
    PageInfo
    Data []InternalDocumentInfo `json:"data"`
}

type InternalDocumentInfo {
    ID string `json:"id"`
    No string `json:"no"`
    VersionNo string `json:"versionNo"`
    OriginalNo string `json:"originalNo"`
    OriginalVersionNo string `json:"originalVersionNo"`
    Name string `json:"name"`
    DocCategoryID string `json:"docCategoryId"`
    DocCategoryName string `json:"docCategoryName"`
    DepartmentID string `json:"departmentId"`
    DepartmentName string `json:"departmentName"`
    AuthorID string `json:"authorId"`
    AuthorNickname string `json:"authorNickname"`
    PublishDate int64 `json:"publishDate"`
    EffectiveDate int64 `json:"effectiveDate"`
    ApprovalInfo ApprovalInfo `json:"approvalInfo"`
    Status int `json:"status"`
}