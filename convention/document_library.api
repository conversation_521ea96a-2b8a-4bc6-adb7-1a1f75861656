syntax = "v1"


type ImportDocumentLibraryReq {
  MainFileID string `json:"mainFileId"`
  ListFileIDs []string `json:"listFileIds,optional"`
  TypeDictionaryID string `json:"typeDictionaryId"` // 类型字典id
  DomainDictionaryID string `json:"domainDictionaryId,optional"` // 领域字典id
  AuthDictionaryID string `json:"authDictionaryId,optional"` // 认证字典id
  ModuleType int `json:"moduleType"` // 模块类型 1-书籍库 2-内部库 3-外部库
}

type ImportDocumentLibraryResp {

}



type ExportDocumentLibraryReq {
  ModuleType int `json:"moduleType"` // 模块类型 1-书籍库 2-内部库 3-外部库
  Params map[string]interface{} `json:"params"` // 参数
}

type ExportDocumentLibraryResp {

}

type GetDocPermissionUsersReq {
  FileID string `form:"fileId"`
  FileForm int32 `form:"fileForm"`
  FilePermission int32 `form:"filePermission"`
}

type GetDocPermissionUsersResp {
  OrganizationUserTree *OrganizationUserTree `json:"organizationUserInfo"`
  Users []OrganizationUsers `json:"users"`
}

type OrganizationUserTree {
  ParentId string `json:"parentId"`
  OrgID string `json:"orgId"`
  OrgName string `json:"orgName"`
  OrganizationUsers []OrganizationUsers `json:"userInfo"`
  Children []*OrganizationUserTree `json:"children"`
}

type OrganizationUsers {
  UserID string `json:"userId"`
  Nickname string `json:"nickname"`
  Status int `json:"status"`
}

type GetDistributeListReq {
  PageInfo
  FileNumber string `form:"fileNumber,optional"` // 文件编号
  FileName string `form:"fileName,optional"` // 文件名称
  FileType int `form:"fileType,optional"` // 文件类型
  FileCategory []string `form:"fileCategory,optional"` // 文件类别
  DistributeType int `form:"distributeType,optional"` // 发放类型
  Status int `form:"status,optional"` // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
  Applicant string `form:"applicant,optional"` // 申请人
}

type GetDistributeListResp {
  Total int64 `json:"total"`
  Data []GetDistributeListInfo `json:"data"`
}

type GetDistributeListInfo {
  ID string `json:"id"` // 主键id
  Applicant string `json:"applicant"` // 申请人
  ApplyDate int64 `json:"applyDate"` // 申请时间
  DistributeType int `json:"distributeType"` // 发放类型，1内部发放 | 2外部发放
  FileType int `json:"fileType"` // 文件类型，1内部文件 | 2外部文件
  FileCategory string `json:"fileCategory"` // 文件类别
  Reason string `json:"reason"` // 发放原因
  OtherReason string `json:"otherReason"` // 其他原因
  WishDistributeDate int64 `json:"wishDistributeDate"` // 期望发放时间
  Status int `json:"status"` // 状态，1待提交 | 2待审批 | 3已审批 | 4已驳回
  WorkflowID string `json:"workflowId"` // 工作流id
  ApprovalInfo ApprovalInfo `json:"approver"` // 审批人
  Received []DistributeUser `json:"received"` // 已接收用户
  NotReceived []DistributeUser `json:"notReceived"` // 未接收用户
  Recycle []DistributeUser `json:"recycle"` // 回收用户
}

type DistributeUser {
  UserID string `json:"userId"`       // 用户id
  FileForm int32 `json:"fileForm"`    // 文件形式（1电子文件 | 2纸质文件）
  Nickname string `json:"nickname"`    // 用户昵称
}

type GetDistributeInventoryReq {
  ID string `form:"id"`
}

type GetDistributeInventoryResp {
  Data []DistributeInventory `json:"data"` // 发放清单
}

type DistributeInventory {
  ID string `json:"id"` // 发放清单id
  FileId string `json:"fileId"` // 文档id
  FileName string `json:"fileName"` // 文档名称
  Number string `json:"number"` // 文档编号
  Version string `json:"version"` // 文档版本
  EFileLook PermissionResp `json:"eFileLook"` // 电子文件查阅权限
  EFileLookAndDownload PermissionResp `json:"eFileLookAndDownload"` // 电子文件查阅和下载权限
  PaperDocumentOnceDownload PermissionResp `json:"paperDocumentOnceDownload"` // 纸质文件一次下载权限
  EFileOnceDownload PermissionResp `json:"eFileOnceDownload"` // 电子文件一次下载权限
}

type PermissionResp {
  FileForm int32 `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
  FilePermission int32 `json:"filePermission"` // 文件权限（1查阅 | 2查阅/下载 | 3一次下载）
  DistributeCount int `json:"distributeCount"` // 发放份数
  RecycleCount int `json:"recycleCount"` // 回收份数
  DisposalCount int `json:"disposalCount"` // 处置份数
  ReceivedBy []ReceivedBy `json:"receivedBy"` // 接收人
}

type ReceivedBy {
  UserID string `json:"userId"` // 用户id
  Nickname string `json:"nickname"` // 用户昵称
  Status int `json:"status"` // 状态（1未回收 | 2回收审批中 | 3已回收 | 4处置审批中 | 5已处置）
}

type DeleteDistributeReq {
  ID string `form:"id"` // 发放列表id
}

type DeleteDistributeResp {
}

// 获取发放详情请求
type GetDistributeDetailReq {
  ID string `form:"id"` // 发放记录ID
}

// 获取发放详情响应
type GetDistributeDetailResp {
   // 基本信息
  ID string `json:"id"` // 发放记录ID
  WorkflowID string `json:"workflowId"` // 流程ID
  Applicant string `json:"applicant"` // 申请人ID
  ApplicantName string `json:"applicantName"` // 申请人姓名
  ApplyDate int64 `json:"applyDate"` // 申请日期

  // 类型信息
  DistributeType int32 `json:"distributeType"` // 发放类型（1内部发放 | 2外部发放）
  FileType int32 `json:"fileType"` // 文件类型（1内部文件 | 2外部文件）
  FileCategory string `json:"fileCategory"` // 文件类别
  TypeDictNodeID string `json:"typeDictNodeId"` // 类型字典节点ID

  // 原因信息
  Reason string `json:"reason"` // 发放原因
  OtherReason string `json:"otherReason"` // 其他原因

  // 日期和状态
  WishDistributeDate int64 `json:"wishDistributeDate"` // 期望发放日期
  Status int32 `json:"status"` // 状态（1待提交 | 2待审批 | 3已审批 | 4已驳回）

  // 发放清单
  DistributeList []DistributeInventoryDetail `json:"distributeList"` // 发放清单

  // 时间戳
  CreatedAt int64 `json:"createdAt"` // 创建时间
  UpdatedAt int64 `json:"updatedAt"` // 更新时间
}


// 发放清单详情
type DistributeInventoryDetail {
  ID string `json:"id"` // 发放清单ID
  FileID string `json:"fileId"` // 文件ID
  FileName string `json:"fileName"` // 文件名称
  Number string `json:"number"` // 文件编号
  Version string `json:"version"` // 文件版本
  Permissions []PermissionDetail `json:"permissions"` // 权限详情列表
}

// 权限详情信息
type PermissionDetail {
  FileForm int32 `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
  FilePermission int32 `json:"filePermission"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
  Recipient string `json:"recipient"` // 接收方
  ReceivedBy []DistributeUserDetail `json:"receivedBy"` // 接收人详情列表
}

// 用户详情信息
type DistributeUserDetail {
  UserID string `json:"userId"` // 用户ID
  UserNickname string `json:"userNickname"` // 用户姓名
  RecycleStatus int32 `json:"recycleStatus"` // 回收状态（0未回收 | 1已回收 | 2待审批）
  RecycleTime int64 `json:"recycleTime"` // 回收时间（Unix时间戳）
}

// 根据发放清单ID查询回收信息请求
type GetRecycleInfoByDistributeIdReq {
  DistributeListID string `form:"distributeListId"` // 发放清单ID
}

// 根据发放清单ID查询回收信息响应
type GetRecycleInfoByDistributeIdResp {
   FileName string `json:"fileName"` // 文件名称
  FileNumber string `json:"fileNumber"` // 文件编号
  RecycleRecords []RecycleRecord `json:"recycleRecords"` // 回收记录列表
}


// 回收记录
type RecycleRecord {
  RecycleInitiator string `json:"recycleInitiator"` // 回收发起人
  RecycleReason string `json:"recycleReason"` // 回收原因
  HandoverPersons []HandoverPerson `json:"handoverPersons"` // 交还人信息列表
  Auditors []string `json:"auditors"` // 审批人列表
  Approvers []string `json:"approvers"` // 批准人列表
  RecycleDate int64 `json:"recycleDate"` // 回收日期（毫秒级时间戳）
}

// 交还人信息
type HandoverPerson {
  HandoverID string `json:"handoverId"` // 交还人ID
  HandoverName string `json:"handoverName"` // 交还人名字
  FileForm int32 `json:"fileForm"` // 文件形式（1电子文件 | 2纸质文件）
  FilePermission int32 `json:"filePermission"` // 文件权限（1查询 | 2查询/下载 | 3一次下载）
}

// 查询处置详情请求
type GetDisposalDetailReq {
  DistributeListID string `form:"distributeListId"` // 发放清单ID
}

// 查询处置详情响应
type GetDisposalDetailResp {
  FileName string `json:"fileName"` // 文件名称
  FileNumber string `json:"fileNumber"` // 文件编号
  DisposalRecords []DisposalRecord `json:"disposalRecords"` // 处置记录列表
}

// 处置记录
type DisposalRecord {
  HandoverPerson string `json:"returnPerson"` // 交还人
  HandoverDate int64 `json:"returnDate"` // 交还日期
  RecyclePerson string `json:"recyclePerson"` // 回收人
  RecycleDate int64 `json:"recycleDate"` // 回收日期
  DisposalPerson string `json:"disposalPerson"` // 处置人
  DisposalDate int64 `json:"disposalDate"` // 处置日期
  DisposalMethod string `json:"disposalMethod"` // 处置方式
}
